name: 'CodeQL'

on:
  push:
    branches:
      - main
      - '!dependabot/**'
  pull_request:
    # The branches below must be a subset of the branches above
    branches:
      - main
  schedule:
    - cron: '0 2 * * 5'

jobs:
  analyze:
    name: Analyze
    runs-on: ubuntu-latest
    permissions:
      actions: read
      contents: read
      security-events: write

    steps:
      - name: Checkout repository
        uses: actions/checkout@v3

      - name: Initialize CodeQL
        uses: github/codeql-action/init@v2
        with:
          languages: 'javascript'

      - name: Perform CodeQL Analysis
        uses: github/codeql-action/analyze@v2
