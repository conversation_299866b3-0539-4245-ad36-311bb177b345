name: Lint

on:
  push:
    branches:
      - main
      - '!dependabot/**'
  pull_request:
    branches:
      - '**'

env:
  FORCE_COLOR: 2
  NODE: 18

permissions:
  contents: read

jobs:
  lint:
    runs-on: ubuntu-latest

    steps:
      - name: Clone repository
        uses: actions/checkout@v3

      - name: Set up Node.js
        uses: actions/setup-node@v3.1.0
        with:
          node-version: ${{ env.NODE }}
          cache: npm

      - name: Install npm dependencies
        run: npm ci

      - name: Run lint
        run: npm run lint
