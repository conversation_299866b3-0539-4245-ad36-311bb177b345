{"name": "npm-check-updates", "version": "15.3.4", "author": "<PERSON> <<EMAIL>>", "license": "Apache-2.0", "contributors": ["<PERSON><PERSON> (https://github.com/raineorshine)"], "description": "Find newer versions of dependencies than what your package.json allows", "keywords": ["npm", "check", "find", "discover", "updates", "upgrades", "dependencies", "package.json", "updater", "version", "management", "ncu"], "engines": {"node": ">=14.14"}, "main": "build/src/index.js", "scripts": {"build": "npm run build:ts && npm run build:options", "build:ts": "tsc", "build:options": "node build/src/scripts/build-options.js && tsc", "lint": "cross-env FORCE_COLOR=1 npm-run-all --parallel --aggregate-output lint:*", "lint:lockfile": "lockfile-lint", "lint:markdown": "markdownlint \"**/*.md\" --ignore node_modules --ignore build --config .markdownlint.js", "lint:src": "eslint --cache --cache-location node_modules/.cache/.eslintcache --ignore-path .gitignore --report-unused-disable-directives .", "c8": "c8", "prepare": "husky install", "prepublishOnly": "npm run build", "prettier": "prettier .", "test": "npm run test:src && npm run test:timeout", "test:src": "mocha test test/package-managers/npm test/package-managers/yarn test/package-managers/staticRegistry", "test:timeout": "mocha --exit test/timeout", "ncu": "node build/src/bin/cli.js"}, "bin": {"npm-check-updates": "build/src/bin/cli.js", "ncu": "build/src/bin/cli.js"}, "repository": {"type": "git", "url": "git+https://github.com/raineorshine/npm-check-updates.git"}, "homepage": "https://github.com/raineorshine/npm-check-updates", "bugs": {"url": "https://github.com/raineorshine/npm-check-updates/issues"}, "dependencies": {"chalk": "^5.0.1", "cli-table": "^0.3.11", "commander": "^9.3.0", "fast-memoize": "^2.5.2", "find-up": "5.0.0", "fp-and-or": "^0.1.3", "get-stdin": "^8.0.0", "globby": "^11.0.4", "hosted-git-info": "^5.0.0", "json-parse-helpfulerror": "^1.0.3", "jsonlines": "^0.1.1", "lodash": "^4.17.21", "minimatch": "^5.1.0", "p-map": "^4.0.0", "pacote": "^13.6.1", "parse-github-url": "^1.0.2", "progress": "^2.0.3", "prompts-ncu": "^2.5.1", "rc-config-loader": "^4.1.0", "remote-git-tags": "^3.0.0", "rimraf": "^3.0.2", "semver": "^7.3.7", "semver-utils": "^1.1.4", "source-map-support": "^0.5.21", "spawn-please": "^1.0.0", "update-notifier": "^6.0.2", "yaml": "^2.1.1"}, "devDependencies": {"@trivago/prettier-plugin-sort-imports": "^3.2.0", "@types/chai": "^4.3.1", "@types/chai-as-promised": "^7.1.5", "@types/chai-string": "^1.4.2", "@types/cli-table": "^0.3.0", "@types/hosted-git-info": "^3.0.2", "@types/json-parse-helpfulerror": "^1.0.1", "@types/jsonlines": "^0.1.2", "@types/lodash": "^4.14.182", "@types/minimatch": "^3.0.5", "@types/mocha": "^9.1.1", "@types/node": "^18.0.4", "@types/pacote": "^11.1.5", "@types/parse-github-url": "^1.0.0", "@types/progress": "^2.0.5", "@types/prompts": "^2.0.14", "@types/remote-git-tags": "^4.0.0", "@types/rimraf": "^3.0.2", "@types/semver": "^7.3.10", "@types/semver-utils": "^1.1.1", "@types/update-notifier": "^6.0.1", "@typescript-eslint/eslint-plugin": "^5.30.6", "@typescript-eslint/parser": "^5.30.6", "c8": "^7.11.3", "chai": "^4.3.6", "chai-as-promised": "^7.1.1", "chai-string": "^1.5.0", "cross-env": "^7.0.3", "eslint": "^8.19.0", "eslint-config-prettier": "^8.5.0", "eslint-config-raine": "^0.3.0", "eslint-config-standard": "^17.0.0", "eslint-plugin-fp": "^2.3.0", "eslint-plugin-import": "^2.26.0", "eslint-plugin-jsdoc": "^39.3.3", "eslint-plugin-n": "^15.2.4", "eslint-plugin-promise": "^6.0.0", "husky": "^8.0.1", "lockfile-lint": "^4.7.6", "markdownlint-cli": "^0.31.1", "mocha": "^10.0.0", "npm-run-all": "^4.1.5", "prettier": "2.7.1", "should": "^13.2.3", "strip-ansi": "^7.0.1", "ts-node": "^10.8.2", "typescript": "^4.7.4", "yarn": "^1.22.19"}, "files": ["build", "!**/test/**"], "lockfile-lint": {"allowed-schemes": ["https:"], "allowed-hosts": ["npm"], "empty-hostname": false, "type": "npm ", "path": "package-lock.json"}, "mocha": {"check-leaks": true, "extension": ["test.ts"], "require": ["source-map-support/register", "ts-node/register"], "timeout": 30000, "trace-deprecation": true, "trace-warnings": true, "use_strict": true}, "c8": {"reporter": ["html", "lcov", "text-summary"]}}