{"dependencies": {"@angular-devkit/architect": "^0.800.0-beta.15", "@angular-devkit/build-optimizer": "^0.800.0-beta.15", "@angular-devkit/core": "^8.0.0-beta.15", "@angular-devkit/schematics": "^8.0.0-beta.15", "@angular/bazel": "file:./tools/npm/@angular_bazel", "@angular/cli": "^8.0.0-beta.15", "@bazel/bazel": "0.27.0", "@bazel/buildifier": "^0.26.0", "@bazel/hide-bazel-files": "0.32.2", "@bazel/ibazel": "~0.9.0", "@bazel/jasmine": "0.32.2", "@bazel/karma": "0.32.2", "@bazel/typescript": "0.32.2", "@microsoft/api-extractor": "^7.0.21", "@schematics/angular": "^8.0.0-beta.15", "@types/angular": "^1.6.47", "@types/base64-js": "1.2.5", "@types/bluebird": "^3.5.27", "@types/chai": "^4.1.2", "@types/chokidar": "^1.7.5", "@types/convert-source-map": "^1.5.1", "@types/diff": "^3.5.1", "@types/fs-extra": "4.0.2", "@types/hammerjs": "2.0.35", "@types/inquirer": "^0.0.44", "@types/jasmine": "^2.8.8", "@types/jasminewd2": "^2.0.6", "@types/minimist": "^1.2.0", "@types/node": "^10.9.4", "@types/selenium-webdriver": "3.0.7", "@types/shelljs": "^0.7.8", "@types/source-map": "^0.5.1", "@types/systemjs": "0.19.32", "@types/yargs": "^11.1.1", "@webcomponents/custom-elements": "^1.0.4", "angular": "npm:angular@1.7", "angular-1.5": "npm:angular@1.5", "angular-1.6": "npm:angular@1.6", "angular-mocks": "npm:angular-mocks@1.7", "angular-mocks-1.5": "npm:angular-mocks@1.5", "angular-mocks-1.6": "npm:angular-mocks@1.6", "art": "^0.10.1", "autoprefixer": "^1.0.0", "babel-cli": "^6.6.5", "babel-code-frame": "^6.26.0", "babel-core": "^1.0.0", "babel-eslint": "^1.0.0", "babel-helper-vue-jsx-merge-props": "^1.0.0", "babel-jest": "^1.0.0", "babel-loader": "^1.0.0", "babel-plugin-check-es2015-constants": "^6.5.0", "babel-plugin-dynamic-import-node": "^1.0.0", "babel-plugin-external-helpers": "^6.22.0", "babel-plugin-syntax-dynamic-import": "^6.18.0", "babel-plugin-syntax-jsx": "^1.0.0", "babel-plugin-syntax-trailing-function-commas": "^6.5.0", "babel-plugin-transform-async-to-generator": "^6.22.0", "babel-plugin-transform-class-properties": "^6.11.5", "babel-plugin-transform-es2015-arrow-functions": "^6.5.2", "babel-plugin-transform-es2015-block-scoped-functions": "^6.5.0", "babel-plugin-transform-es2015-block-scoping": "^6.23.0", "babel-plugin-transform-es2015-classes": "^6.5.2", "babel-plugin-transform-es2015-computed-properties": "^6.5.2", "babel-plugin-transform-es2015-destructuring": "^6.5.0", "babel-plugin-transform-es2015-for-of": "^6.5.2", "babel-plugin-transform-es2015-literals": "^6.5.0", "babel-plugin-transform-es2015-modules-commonjs": "^6.5.2", "babel-plugin-transform-es2015-object-super": "^6.5.0", "babel-plugin-transform-es2015-parameters": "^6.5.0", "babel-plugin-transform-es2015-shorthand-properties": "^6.5.0", "babel-plugin-transform-es2015-spread": "^6.5.2", "babel-plugin-transform-es2015-template-literals": "^6.5.2", "babel-plugin-transform-object-rest-spread": "^6.6.5", "babel-plugin-transform-react-jsx-source": "^6.8.0", "babel-plugin-transform-regenerator": "^6.26.0", "babel-plugin-transform-runtime": "^1.0.0", "babel-plugin-transform-vue-jsx": "^1.0.0", "babel-preset-env": "^1.0.0", "babel-preset-react": "^6.5.0", "babel-preset-stage-2": "^1.0.0", "babel-traverse": "^6.9.0", "babylon": "6.18.0", "base64-js": "1.2.1", "bluebird": "^3.5.5", "brotli": "^1.3.2", "browserstacktunnel-wrapper": "2.0.1", "canonical-path": "1.0.0", "chai": "^4.1.2", "chalk": "^1.0.0", "check-side-effects": "0.0.21", "chokidar": "^2.1.1", "clang-format": "1.0.41", "cldr": "4.10.0", "cldr-data-downloader": "0.3.2", "cldrjs": "0.5.0", "cli-table": "^0.3.1", "coffee-script": "^1.8.0", "conventional-changelog": "^2.0.3", "convert-source-map": "^1.5.1", "copy-webpack-plugin": "^1.0.0", "core-js": "^2.2.1", "cors": "2.8.4", "coveralls": "^2.11.6", "create-react-class": "^15.6.3", "cross-env": "^5.1.1", "css-loader": "^0.28.0", "danger": "^3.0.4", "dependency-graph": "^0.7.2", "diff": "^3.5.0", "domino": "2.1.2", "entities": "1.1.1", "error-stack-parser": "^2.0.2", "eslint": "^1.0.0", "eslint-config-airbnb-base": "^1.0.0", "eslint-config-fbjs": "^1.1.1", "eslint-friendly-formatter": "^1.0.0", "eslint-import-resolver-webpack": "^0.8.3", "eslint-loader": "^1.0.0", "eslint-plugin-babel": "^3.3.0", "eslint-plugin-flowtype": "^2.25.0", "eslint-plugin-import": "^1.0.0", "eslint-plugin-jest": "^21.6.1", "eslint-plugin-no-for-of-loops": "^1.0.0", "eslint-plugin-react": "^6.7.1", "eslint-plugin-react-internal": "link:./scripts/eslint-rules/", "eslint-plugin-vue": "^1.0.0", "extract-text-webpack-plugin": "^1.0.0", "fbjs-scripts": "^0.8.3", "file-loader": "^1.0.0", "filesize": "^3.5.6", "firebase-tools": "5.1.1", "firefox-profile": "1.0.3", "flow-bin": "^0.72.0", "friendly-errors-webpack-plugin": "^1.0.0", "fs-extra": "4.0.2", "glob": "7.1.2", "glob-stream": "^6.1.0", "google-closure-compiler": "20190301.0.0", "gulp": "3.9.1", "gulp-clang-format": "1.0.23", "gulp-connect": "5.0.0", "gulp-conventional-changelog": "^2.0.3", "gulp-filter": "^5.1.0", "gulp-git": "^2.7.0", "gulp-tslint": "8.1.2", "gzip-size": "^3.0.0", "hammerjs": "2.0.8", "html-webpack-plugin": "^1.0.0", "husky": "^0.14.3", "incremental-dom": "0.4.1", "jasmine": "^3.1.0", "jasmine-check": "^1.0.0-rc.0", "jasmine-core": "^3.1.0", "jest": "^1.0.0", "jest-diff": "^23.0.1", "jest-serializer-vue": "^0.3.0", "jest-snapshot-serializer-raw": "^1.1.0", "jpm": "1.3.1", "jquery": "3.0.0", "karma": "^3.1.4", "karma-browserstack-launcher": "^1.3.0", "karma-chrome-launcher": "^2.2.0", "karma-jasmine": "^1.1.2", "karma-sauce-launcher": "^2.0.2", "karma-sourcemap-loader": "^0.3.7", "madge": "0.5.0", "magic-string": "^0.25.0", "materialize-css": "1.0.0", "minimatch": "^3.0.4", "minimist": "^1.2.0", "mkdirp": "^0.5.1", "mutation-observer": "^1.0.3", "ncp": "^2.0.0", "node-notifier": "^1.0.0", "node-uuid": "1.4.8", "nodejs-websocket": "^1.7.2", "object-assign": "^4.1.1", "optimize-css-assets-webpack-plugin": "^1.0.0", "ora": "^1.0.0", "portfinder": "^1.0.0", "postcss-import": "^1.0.0", "postcss-loader": "^1.0.0", "postcss-url": "^1.0.0", "prettier": "1.13.7", "prop-types": "^15.6.2", "protractor": "^5.4.2", "random-seed": "^0.3.0", "react-lifecycles-compat": "^3.0.2", "reflect-metadata": "^0.1.3", "rewire": "2.5.2", "rimraf": "^1.0.0", "rollup": "^0.52.1", "rollup-plugin-amd": "^3.0.0", "rollup-plugin-babel": "^3.0.1", "rollup-plugin-commonjs": "^8.2.6", "rollup-plugin-json": "^4.0.0", "rollup-plugin-node-resolve": "^2.1.1", "rollup-plugin-prettier": "^0.3.0", "rollup-plugin-replace": "^2.0.0", "rollup-plugin-sourcemaps": "^0.4.2", "rollup-plugin-strip-banner": "^0.2.0", "rxjs": "^6.4.0", "sauce-connect": "https://saucelabs.com/downloads/sc-4.5.1-linux.tar.gz", "selenium-webdriver": "3.5.0", "semver": "^5.5.0", "shelljs": "^0.8.1", "source-map": "^0.6.1", "source-map-support": "0.5.9", "systemjs": "0.18.10", "targz": "^1.0.1", "through2": "^2.0.0", "tmp": "~0.0.28", "tsickle": "0.34.3", "tslib": "^1.9.0", "tslint": "5.7.0", "tslint-eslint-rules": "4.1.1", "tslint-no-toplevel-property-access": "0.0.2", "tsutils": "2.27.2", "typescript": "~1.8.10", "uglifyjs-webpack-plugin": "^1.0.0", "universal-analytics": "0.4.15", "url-loader": "^0.5.8", "vlq": "0.2.2", "vrsource-tslint-rules": "5.1.1", "vue-jest": "^1.0.0", "vue-loader": "^1.0.0", "vue-style-loader": "^1.0.0", "vue-template-compiler": "^1.0.0", "webpack": "1.12.9", "webpack-bundle-analyzer": "^1.0.0", "webpack-dev-server": "^1.0.0", "webpack-merge": "^1.0.0", "xhr2": "0.1.4", "yargs": "13.1.0", "zone.js": "^0.9.1"}}