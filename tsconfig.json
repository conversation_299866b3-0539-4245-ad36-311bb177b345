{"include": ["src", "test"], "exclude": ["test/deep", "test/doctor"], "compilerOptions": {"allowJs": true, "allowSyntheticDefaultImports": true, "declaration": true, "esModuleInterop": true, "forceConsistentCasingInFileNames": true, "isolatedModules": true, "lib": ["es2019"], "module": "commonjs", "moduleResolution": "nodenext", "noFallthroughCasesInSwitch": true, "noImplicitAny": true, "paths": {"libnpmconfig": ["./src/types/libnpmconfig"], "prompts-ncu": ["./src/types/prompts-ncu"], "spawn-please": ["./src/types/spawn-please"]}, "resolveJsonModule": true, "outDir": "./build", "skipLibCheck": true, "sourceMap": true, "strict": true, "target": "es2019"}}